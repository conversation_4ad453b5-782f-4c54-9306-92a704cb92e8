import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import VPSAdminChat from './VPSAdminChat';
import { useTheme } from './hooks/useTheme';

// Theme wrapper component to handle dark mode
const ThemeWrapper = () => {
  const { effectiveTheme } = useTheme();

  return (
    <div className={effectiveTheme === 'dark' ? 'dark' : ''}>
      <div className="flex items-center justify-center h-screen lg:min-h-screen bg-gradient-to-br from-slate-100 via-slate-200 to-slate-300 dark:from-slate-900 dark:via-slate-800 dark:to-slate-700 p-0 lg:p-4 overflow-hidden lg:overflow-auto transition-all duration-500">
        <VPSAdminChat />
      </div>
    </div>
  );
};

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ThemeWrapper />
  </StrictMode>,
)
