/**
 * ConfirmationButtons component for command confirmation
 */

import React, { useState } from 'react';
import { CheckC<PERSON><PERSON>, XCircle, SkipForward, RotateCcw, Square, ChevronDown, ChevronUp } from 'lucide-react';
import { ConfirmationButtonsProps } from '../types';

const ConfirmationButtons: React.FC<ConfirmationButtonsProps> = ({
  onConfirm,
  isVisible,
  options = [],
  showAdvancedOptions = false
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  if (!isVisible) return null;

  const hasAdvancedOptions = showAdvancedOptions || options.includes('skip_step') || options.includes('retry_step') || options.includes('abort_task');

  return (
    <div className="mt-3 pt-2 border-t border-white/20 dark:border-slate-400/20">
      {/* Primary confirmation buttons */}
      <div className="flex justify-end items-center gap-2">
        <p className="text-xs font-medium text-white/90 mr-auto">Confirm execution?</p>

        {hasAdvancedOptions && (
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="px-2 py-1 text-xs font-medium bg-slate-600 dark:bg-slate-500 text-white rounded-lg hover:bg-slate-700 dark:hover:bg-slate-400 transition-all duration-200 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-slate-400 shadow-md hover:shadow-lg"
          >
            {showAdvanced ? <ChevronUp size={12} /> : <ChevronDown size={12} />}
            Options
          </button>
        )}

        <button
          onClick={() => onConfirm('yes')}
          className="px-2.5 py-1 text-xs font-medium bg-emerald-500 dark:bg-emerald-600 text-white rounded-lg hover:bg-emerald-600 dark:hover:bg-emerald-500 transition-all duration-200 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-emerald-400 shadow-md hover:shadow-lg"
        >
          <CheckCircle size={12} /> Yes
        </button>
        <button
          onClick={() => onConfirm('no')}
          className="px-2.5 py-1 text-xs font-medium bg-red-500 dark:bg-red-600 text-white rounded-lg hover:bg-red-600 dark:hover:bg-red-500 transition-all duration-200 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-red-400 shadow-md hover:shadow-lg"
        >
          <XCircle size={12} /> No
        </button>
      </div>

      {/* Advanced options */}
      {showAdvanced && hasAdvancedOptions && (
        <div className="mt-2 pt-2 border-t border-white/10 dark:border-slate-400/10 flex justify-end items-center gap-2">
          <p className="text-xs font-medium text-white/70 mr-auto">Advanced options:</p>

          {(options.includes('skip_step') || showAdvancedOptions) && (
            <button
              onClick={() => onConfirm('skip_step')}
              className="px-2.5 py-1 text-xs font-medium bg-amber-500 dark:bg-amber-600 text-white rounded-lg hover:bg-amber-600 dark:hover:bg-amber-500 transition-all duration-200 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-amber-400 shadow-md hover:shadow-lg"
            >
              <SkipForward size={12} /> Skip Step
            </button>
          )}

          {(options.includes('retry_step') || showAdvancedOptions) && (
            <button
              onClick={() => onConfirm('retry_step')}
              className="px-2.5 py-1 text-xs font-medium bg-sky-500 dark:bg-sky-600 text-white rounded-lg hover:bg-sky-600 dark:hover:bg-sky-500 transition-all duration-200 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-sky-400 shadow-md hover:shadow-lg"
            >
              <RotateCcw size={12} /> Retry Step
            </button>
          )}

          {(options.includes('abort_task') || showAdvancedOptions) && (
            <button
              onClick={() => onConfirm('abort_task')}
              className="px-2.5 py-1 text-xs font-medium bg-red-700 dark:bg-red-800 text-white rounded-lg hover:bg-red-800 dark:hover:bg-red-700 transition-all duration-200 flex items-center gap-1 active:scale-95 focus:outline-none focus:ring-2 focus:ring-red-600 shadow-md hover:shadow-lg"
            >
              <Square size={12} /> Abort Task
            </button>
          )}
        </div>
      )}
    </div>
  );
};

export default ConfirmationButtons;
